import json
from accounts.models import UserProfile
from auth.scripts import check_user_auth, fetch_devices
from channels.generic.websocket import WebsocketConsumer
from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer
from django.db.models import Q
from notification_center.models import Event, Notification
import logging

logger = logging.getLogger("app")


class DeviceEventsConsumer(WebsocketConsumer):
    def connect(self):
        self.user = self.scope["user"]

        self.accept()

        check_user_auth(self)

    def disconnect(self, close_code):
        # Leave field group if it exists
        if hasattr(self, "devices"):
            devices = getattr(self, "devices", [])
            for device in devices:
                async_to_sync(get_channel_layer().group_discard)(
                    f"deviceEvents_{device.id}", self.channel_name
                )

    def receive(self, text_data):
        fetch_devices(self, text_data)
        self.handle_subscription()
        self.send_initial_data()

    def handle_subscription(self):
        for device in self.devices:
            async_to_sync(get_channel_layer().group_add)(
                f"deviceEvents_{device.id}", self.channel_name
            )

    def send_initial_data(self):
        events = Event.objects.filter(devi__in=self.devices).order_by("-crat")[:25]
        events_list = [event.to_dict() for event in events]
        self.send(text_data=json.dumps({"type": "init", "data": events_list}))

    def object_update(self, event):
        # Ensure we're sending a consistent format
        if isinstance(event["data"], dict) and "type" not in event["data"]:
            self.send(text_data=json.dumps({"type": "object_update", "data": event["data"]}))
        else:
            self.send(text_data=json.dumps(event["data"]))


class UserNotificationsConsumer(WebsocketConsumer):
    def connect(self):
        self.user = self.scope["user"]

        self.accept()

        check_user_auth(self)

        self.notifications_group_name = f"notifications_{self.user.id}"

        # Join device group
        async_to_sync(get_channel_layer().group_add)(
            self.notifications_group_name, self.channel_name
        )

        self.send_initial_data()

    def disconnect(self, close_code):
        # Leave notifications group if it exists
        if hasattr(self, "notifications_group_name"):
            async_to_sync(get_channel_layer().group_discard)(
                self.notifications_group_name, self.channel_name
            )

    def receive(self):
        pass

    def send_initial_data(self):
        # get unread notifications and events
        notifications = Notification.objects.filter(
            user=self.user.id, read=False
        ).order_by("-evnt__crat")[:25]

        notifications_json = [notification.to_dict() for notification in notifications]

        self.send(json.dumps({"type": "init", "data": notifications_json}))

    def object_update(self, event):
        self.send(json.dumps(event))
