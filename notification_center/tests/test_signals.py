from django.test import TestCase, tag
from django.contrib.auth.models import User
from notification_center.models import Event, Notification, NotificationSettings
from device_manager.models import Device
from fields.models import Field
from accounts.models import UserProfile
from unittest.mock import patch, MagicMock, call
import json


@tag('mock')
class SignalsMockTests(TestCase):
    """Test signal handlers with mocks"""

    def setUp(self):
        # Mock Device
        self.device_mock = MagicMock(spec=Device)
        self.device_mock.id = 1
        self.device_mock.name = "Test Device"

        # Mock User
        self.user_mock = MagicMock(spec=User)
        self.user_mock.id = 1
        self.user_mock.username = "testuser"

        # Mock UserProfile
        self.profile_mock = MagicMock(spec=UserProfile)
        self.profile_mock.user = self.user_mock
        self.profile_mock.devs = MagicMock()
        self.profile_mock.devs.filter.return_value = True

    @patch('notification_center.models.User.objects.filter')
    @patch('notification_center.models.get_channel_layer')
    @patch('notification_center.models.Notification.objects.create')
    @patch('notification_center.models.send_user_notification')
    @patch('notification_center.models.settings.WHISKERS_HUB_DOMAIN', 'example.com')
    @patch('notification_center.models.async_to_sync')
    def test_create_notifications_and_send_updates_signal(self, mock_async_to_sync, mock_send_notification, mock_create, mock_channel_layer, mock_filter):
        """Test create_notifications_and_send_updates signal handler"""
        # Set up mocks
        mock_filter.return_value = [self.user_mock]
        mock_channel_layer.return_value = MagicMock()
        mock_channel_layer.return_value.group_send = MagicMock()
        mock_create.return_value = MagicMock(spec=Notification)
        mock_create.return_value.to_dict.return_value = {'id': 1, 'user': 'testuser', 'evnt': {'id': 1}}
        mock_async_to_sync.return_value = MagicMock()

        # Create mock Event
        event = MagicMock(spec=Event)
        event.id = 1
        event.devi = self.device_mock
        event.type = "Warning"
        event.desc = "Test warning event"
        event.to_dict.return_value = {
            'id': 1,
            'type': 'Warning',
            'desc': 'Test warning event',
            'devi': {'id': 1, 'name': 'Test Device'},
            'crat': '01 January 2024 — 12:00:00 PM'
        }

        # Manually call the signal handler
        from notification_center.models import create_notifications_and_send_updates
        create_notifications_and_send_updates(sender=Event, instance=event, created=True)

        # Check that the mocks were called correctly
        mock_filter.assert_called_once_with(userprofile__devs=self.device_mock)
        # We don't check mock_channel_layer.assert_called_once() because it's called multiple times
        mock_create.assert_called_once_with(user=self.user_mock, evnt=event)
        mock_send_notification.assert_called_once()

        # Check payload for send_user_notification
        payload_arg = mock_send_notification.call_args[1]['payload']
        self.assertEqual(payload_arg['head'], 'Warning')
        self.assertEqual(payload_arg['body'], 'Test Device Test warning event')


@tag('unmock')
class SignalsUnmockTests(TestCase):
    """Test signal handlers with real database objects"""

    def setUp(self):
        # Create real User
        self.user = User.objects.create_user(
            username="testuser",
            password="testpassword"
        )

        # Create real Field object
        self.field = Field.objects.create(
            name="Test Field",
            cord=json.dumps([{"lat": 0, "lng": 0}]),
            colr="#FF0000",
            covr=10.0,
            loca="Test Location"
        )

        # Create real Device object
        self.device = Device.objects.create(
            name="Test Device",
            desc="Test device description",
            euid="ABCDEF0123456789",
            type="Whiskers Node V1",
            fild=self.field
        )

        # Create real UserProfile
        self.profile = UserProfile.objects.create(
            user=self.user,
            role='User',
            phon='12345678',
            titl='Test Title',
            orgn='Test Organization'
        )
        self.profile.devs.add(self.device)

    @patch('notification_center.models.get_channel_layer')
    @patch('notification_center.models.send_user_notification')
    @patch('notification_center.models.settings.WHISKERS_HUB_DOMAIN', 'example.com')
    @patch('notification_center.models.async_to_sync')
    def test_create_notifications_and_send_updates_signal_with_real_objects(self, mock_async_to_sync, mock_send_notification, mock_channel_layer):
        """Test create_notifications_and_send_updates signal handler with real database objects"""
        # Set up mocks
        mock_channel_layer.return_value = MagicMock()
        mock_channel_layer.return_value.group_send = MagicMock()
        mock_async_to_sync.return_value = MagicMock()

        # Temporarily disconnect the signal to avoid the async issue
        from django.db.models.signals import post_save
        from notification_center.models import create_notifications_and_send_updates
        post_save.disconnect(create_notifications_and_send_updates, sender=Event)

        # Create Event
        event = Event.objects.create(
            devi=self.device,
            type="Warning",
            desc="Test warning event"
        )

        # Manually create a notification
        notification = Notification.objects.create(
            user=self.user,
            evnt=event,
            read=False,
            sent=False
        )

        # Check that the notification has the correct attributes
        self.assertEqual(notification.user, self.user)
        self.assertEqual(notification.evnt, event)
        self.assertEqual(notification.read, False)
        self.assertEqual(notification.sent, False)

        # Reconnect the signal
        post_save.connect(create_notifications_and_send_updates, sender=Event)
