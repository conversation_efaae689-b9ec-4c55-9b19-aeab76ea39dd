from django.test import TestCase, tag
from django.contrib.auth.models import User
from notification_center.models import Event, Notification, NotificationSettings
from notification_center.consumers import DeviceEventsConsumer, UserNotificationsConsumer
from device_manager.models import Device
from fields.models import Field
from channels.testing import WebsocketCommunicator
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
from unittest.mock import patch, MagicMock, AsyncMock
import json


@tag('mock')
class DeviceEventsConsumerMockTests(TestCase):
    """Test DeviceEventsConsumer with mocks"""

    def setUp(self):
        # Mock User
        self.user_mock = MagicMock(spec=User)
        self.user_mock.id = 1
        self.user_mock.username = "testuser"
        self.user_mock.is_authenticated = True

        # Mock Device
        self.device_mock = MagicMock(spec=Device)
        self.device_mock.id = 1
        self.device_mock.name = "Test Device"

        # Mock Event
        self.event_mock = MagicMock(spec=Event)
        self.event_mock.id = 1
        self.event_mock.devi = self.device_mock
        self.event_mock.type = "Warning"
        self.event_mock.desc = "Test warning event"
        self.event_mock.to_dict.return_value = {
            'id': 1,
            'type': 'Warning',
            'desc': 'Test warning event',
            'devi': {'id': 1, 'name': 'Test Device'},
            'crat': '01 January 2024 — 12:00:00 PM'
        }

    @patch('notification_center.consumers.check_user_auth')
    @patch('notification_center.consumers.fetch_devices')
    @patch('notification_center.consumers.get_channel_layer')
    def test_device_events_consumer_connect(self, mock_channel_layer, mock_fetch_devices, mock_check_auth):
        """Test DeviceEventsConsumer connect method"""
        # Set up mocks
        mock_channel_layer.return_value = MagicMock()
        mock_fetch_devices.return_value = None
        mock_check_auth.return_value = None

        # Create consumer
        consumer = DeviceEventsConsumer()
        consumer.scope = {'user': self.user_mock}
        consumer.channel_name = 'test_channel'
        consumer.accept = MagicMock()

        # Call connect
        consumer.connect()

        # Check that accept was called
        consumer.accept.assert_called_once()
        mock_check_auth.assert_called_once_with(consumer)

    @patch('notification_center.consumers.fetch_devices')
    def test_device_events_consumer_receive(self, mock_fetch_devices):
        """Test DeviceEventsConsumer receive method"""
        # Set up mocks
        mock_fetch_devices.return_value = None

        # Create consumer
        consumer = DeviceEventsConsumer()
        consumer.scope = {'user': self.user_mock}
        consumer.devices = [self.device_mock]
        consumer.handle_subscription = MagicMock()
        consumer.send_initial_data = MagicMock()

        # Call receive
        consumer.receive(json.dumps({'devices': [1]}))

        # Check that methods were called
        mock_fetch_devices.assert_called_once_with(consumer, json.dumps({'devices': [1]}))
        consumer.handle_subscription.assert_called_once()
        consumer.send_initial_data.assert_called_once()

    @patch('notification_center.consumers.get_channel_layer')
    @patch('notification_center.consumers.async_to_sync')
    def test_device_events_consumer_handle_subscription(self, mock_async_to_sync, mock_channel_layer):
        """Test DeviceEventsConsumer handle_subscription method"""
        # Set up mocks
        mock_channel_layer.return_value = MagicMock()
        mock_channel_layer.return_value.group_add = AsyncMock()
        mock_async_to_sync.return_value = MagicMock()

        # Create consumer
        consumer = DeviceEventsConsumer()
        consumer.scope = {'user': self.user_mock}
        consumer.devices = [self.device_mock]
        consumer.channel_name = 'test_channel'

        # Call handle_subscription
        consumer.handle_subscription()

        # Check that async_to_sync was called with group_add
        mock_async_to_sync.assert_called_with(mock_channel_layer.return_value.group_add)

        # Check that the wrapped function was called with the correct arguments
        mock_async_to_sync.return_value.assert_called_once_with(
            f"deviceEvents_{self.device_mock.id}", consumer.channel_name
        )


@tag('mock')
class UserNotificationsConsumerMockTests(TestCase):
    """Test UserNotificationsConsumer with mocks"""

    def setUp(self):
        # Mock User
        self.user_mock = MagicMock(spec=User)
        self.user_mock.id = 1
        self.user_mock.username = "testuser"
        self.user_mock.is_authenticated = True

        # Mock Notification
        self.notification_mock = MagicMock(spec=Notification)
        self.notification_mock.id = 1
        self.notification_mock.user = self.user_mock
        self.notification_mock.read = False
        self.notification_mock.to_dict.return_value = {
            'id': 1,
            'user': 'testuser',
            'evnt': {
                'id': 1,
                'type': 'Warning',
                'desc': 'Test warning event',
                'devi': {'id': 1, 'name': 'Test Device'},
                'crat': '01 January 2024 — 12:00:00 PM'
            }
        }

    @patch('notification_center.consumers.check_user_auth')
    @patch('notification_center.consumers.get_channel_layer')
    @patch('notification_center.consumers.async_to_sync')
    def test_user_notifications_consumer_connect(self, mock_async_to_sync, mock_channel_layer, mock_check_auth):
        """Test UserNotificationsConsumer connect method"""
        # Set up mocks
        mock_channel_layer.return_value = MagicMock()
        mock_channel_layer.return_value.group_add = AsyncMock()
        mock_check_auth.return_value = None
        mock_async_to_sync.return_value = MagicMock()

        # Create consumer
        consumer = UserNotificationsConsumer()
        consumer.scope = {'user': self.user_mock}
        consumer.channel_name = 'test_channel'
        consumer.accept = MagicMock()
        consumer.send_initial_data = MagicMock()

        # Call connect
        consumer.connect()

        # Check that methods were called
        consumer.accept.assert_called_once()
        mock_check_auth.assert_called_once_with(consumer)
        self.assertEqual(consumer.notifications_group_name, f"notifications_{self.user_mock.id}")

        # Check that async_to_sync was called with group_add
        mock_async_to_sync.assert_called_with(mock_channel_layer.return_value.group_add)

        # Check that the wrapped function was called with the correct arguments
        mock_async_to_sync.return_value.assert_called_with(
            f"notifications_{self.user_mock.id}", consumer.channel_name
        )

        consumer.send_initial_data.assert_called_once()

    @patch('notification_center.consumers.Notification.objects.filter')
    def test_user_notifications_consumer_send_initial_data(self, mock_filter):
        """Test UserNotificationsConsumer send_initial_data method"""
        # Set up mocks
        mock_filter.return_value.order_by.return_value.__getitem__.return_value = [self.notification_mock]

        # Create consumer
        consumer = UserNotificationsConsumer()
        consumer.scope = {'user': self.user_mock}
        consumer.user = self.user_mock  # Add user attribute explicitly
        consumer.send = MagicMock()

        # Call send_initial_data
        consumer.send_initial_data()

        # Check that methods were called
        mock_filter.assert_called_once_with(user=self.user_mock.id, read=False)
        mock_filter.return_value.order_by.assert_called_once_with("-evnt__crat")
        consumer.send.assert_called_once()

        # Check that the correct data was sent
        call_args = consumer.send.call_args[0][0]
        data = json.loads(call_args)
        self.assertEqual(data['type'], 'init')
        self.assertEqual(len(data['data']), 1)

    def test_user_notifications_consumer_object_update(self):
        """Test UserNotificationsConsumer object_update method"""
        # Create consumer
        consumer = UserNotificationsConsumer()
        consumer.send = MagicMock()

        # Create event data
        event_data = {
            'type': 'object_update',
            'data': self.notification_mock.to_dict()
        }

        # Call object_update
        consumer.object_update(event_data)

        # Check that send was called with the correct data
        consumer.send.assert_called_once_with(json.dumps(event_data))


@tag('unmock')
class ConsumersUnmockTests(TestCase):
    """Test consumers with real database objects"""

    def setUp(self):
        # Create real User
        self.user = User.objects.create_user(
            username="testuser",
            password="testpassword"
        )

        # Create real Field object
        self.field = Field.objects.create(
            name="Test Field",
            cord=json.dumps([{"lat": 0, "lng": 0}]),
            colr="#FF0000",
            covr=10.0,
            loca="Test Location"
        )

        # Create real Device object
        self.device = Device.objects.create(
            name="Test Device",
            desc="Test device description",
            euid="ABCDEF0123456789",
            type="Whiskers Node V1",
            fild=self.field
        )

        # Create real Event object
        self.event = Event.objects.create(
            devi=self.device,
            type="Warning",
            desc="Test warning event"
        )

        # Create real Notification object
        self.notification = Notification.objects.create(
            user=self.user,
            evnt=self.event,
            read=False,
            sent=False
        )

    @patch('notification_center.consumers.check_user_auth')
    @patch('notification_center.consumers.get_channel_layer')
    @patch('notification_center.consumers.async_to_sync')
    def test_device_events_consumer_with_real_objects(self, mock_async_to_sync, mock_channel_layer, mock_check_auth):
        """Test DeviceEventsConsumer with real database objects"""
        # Set up mocks
        mock_channel_layer.return_value = MagicMock()
        mock_channel_layer.return_value.group_add = AsyncMock()
        mock_check_auth.return_value = None
        mock_async_to_sync.return_value = MagicMock()

        # Create consumer
        consumer = DeviceEventsConsumer()
        consumer.scope = {'user': self.user}
        consumer.channel_name = 'test_channel'
        consumer.accept = MagicMock()
        consumer.send = MagicMock()

        # Call connect
        consumer.connect()

        # Set devices and call handle_subscription
        consumer.devices = [self.device]
        consumer.handle_subscription()

        # Check that async_to_sync was called with group_add
        mock_async_to_sync.assert_called_with(mock_channel_layer.return_value.group_add)

        # Check that the wrapped function was called with the correct arguments
        mock_async_to_sync.return_value.assert_called_with(
            f"deviceEvents_{self.device.id}", consumer.channel_name
        )

    @patch('notification_center.consumers.check_user_auth')
    @patch('notification_center.consumers.get_channel_layer')
    @patch('notification_center.consumers.async_to_sync')
    def test_user_notifications_consumer_with_real_objects(self, mock_async_to_sync, mock_channel_layer, mock_check_auth):
        """Test UserNotificationsConsumer with real database objects"""
        # Set up mocks
        mock_channel_layer.return_value = MagicMock()
        mock_channel_layer.return_value.group_add = AsyncMock()
        mock_check_auth.return_value = None
        mock_async_to_sync.return_value = MagicMock()

        # Create consumer
        consumer = UserNotificationsConsumer()
        consumer.scope = {'user': self.user}
        consumer.user = self.user  # Add user attribute explicitly
        consumer.channel_name = 'test_channel'
        consumer.accept = MagicMock()
        consumer.send = MagicMock()

        # Call connect
        consumer.connect()

        # Check that async_to_sync was called with group_add
        mock_async_to_sync.assert_called_with(mock_channel_layer.return_value.group_add)

        # Check that the wrapped function was called with the correct arguments
        mock_async_to_sync.return_value.assert_called_with(
            f"notifications_{self.user.id}", consumer.channel_name
        )

        # Check that send was called with the correct data
        consumer.send.assert_called_once()
        call_args = consumer.send.call_args[0][0]
        data = json.loads(call_args)
        self.assertEqual(data['type'], 'init')
        self.assertEqual(len(data['data']), 1)
        self.assertEqual(data['data'][0]['id'], self.notification.id)
