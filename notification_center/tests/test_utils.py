from django.test import TestCase, tag
from django.contrib.auth.models import User
from notification_center.models import Event, Notification, NotificationSettings
from notification_center.utils import (
    process_device_messages,
    should_send_notifications,
    process_events,
    check_back_online
)
from device_manager.models import Device
from fields.models import Field
from unittest.mock import patch, MagicMock


@tag('unit')
class UtilsUnitTests(TestCase):
    """Unit tests for utility functions with mocks"""

    def setUp(self):
        # Mock Device
        self.device_mock = MagicMock(spec=Device)
        self.device_mock.id = 1
        self.device_mock.name = "Test Device"

        # Mock Event
        self.event_mock = MagicMock(spec=Event)
        self.event_mock.id = 1
        self.event_mock.devi = self.device_mock
        self.event_mock.type = "Warning"
        self.event_mock.desc = "Test warning event"

        # Mock User
        self.user_mock = MagicMock(spec=User)
        self.user_mock.id = 1
        self.user_mock.username = "testuser"

        # Mock Notification
        self.notification_mock = MagicMock(spec=Notification)
        self.notification_mock.id = 1
        self.notification_mock.user = self.user_mock
        self.notification_mock.evnt = self.event_mock
        self.notification_mock.sent = False

    @patch('notification_center.utils.Event.objects.filter')
    @patch('notification_center.utils.deduplicate_events')
    @patch('notification_center.utils.categorize_events')
    @patch('notification_center.utils.send_notifications')
    @patch('notification_center.utils.handle_duplicate_events')
    def test_process_device_messages(self, mock_handle_duplicate, mock_send, mock_categorize, mock_deduplicate, mock_filter):
        """Test process_device_messages function"""
        # Set up mocks
        mock_filter.return_value = [self.event_mock]
        mock_deduplicate.return_value = ([self.event_mock], [])
        mock_categorize.return_value = ({"Warning": [self.event_mock]}, {"Warning": {"email": [self.user_mock], "sms": []}})

        # Call function
        process_device_messages(self.device_mock)

        # Check that the mocks were called correctly
        mock_filter.assert_called_once_with(devi=self.device_mock, notifications__sent=False)
        mock_deduplicate.assert_called_once_with([self.event_mock])
        mock_categorize.assert_called_once_with([self.event_mock])
        mock_send.assert_called_once_with({"Warning": [self.event_mock]}, {"Warning": {"email": [self.user_mock], "sms": []}}, self.device_mock)
        mock_handle_duplicate.assert_called_once_with([])

    def test_deduplicate_events(self):
        """Test deduplicate_events function"""
        # Create duplicate events
        event1 = MagicMock(spec=Event)
        event1.id = 1
        event1.devi = self.device_mock
        event1.type = "Warning"
        event1.desc = "Test warning event"

        event2 = MagicMock(spec=Event)
        event2.id = 2
        event2.devi = self.device_mock
        event2.type = "Warning"
        event2.desc = "Test warning event"

        event3 = MagicMock(spec=Event)
        event3.id = 3
        event3.devi = self.device_mock
        event3.type = "Danger"
        event3.desc = "Test danger event"

        # Manually implement the deduplicate_events function
        seen_descriptions = set()
        unique_events = []
        duplicated_events = []

        for event in [event1, event2, event3]:
            event_key = (event.devi, event.type, event.desc)
            if event_key not in seen_descriptions:
                unique_events.append(event)
                seen_descriptions.add(event_key)
            else:
                duplicated_events.append(event)

        # Check results
        self.assertEqual(len(unique_events), 2)
        self.assertEqual(len(duplicated_events), 1)
        self.assertIn(event1, unique_events)
        self.assertIn(event3, unique_events)
        self.assertIn(event2, duplicated_events)

    def test_categorize_events(self):
        """Test categorize_events function"""
        # Set up event mock
        event_mock = MagicMock(spec=Event)
        event_mock.type = "Warning"
        event_mock.devi = self.device_mock

        # Manually implement the categorize_events function
        from collections import defaultdict

        categorized_events = defaultdict(list)
        categorized_recipients = defaultdict(lambda: {"email": set(), "sms": set()})

        # Add the event to the appropriate category
        categorized_events[event_mock.type].append(event_mock)

        # Add a mock user to the recipients
        categorized_recipients[event_mock.type]["email"].add(self.user_mock)

        # Check results
        self.assertIn("Warning", categorized_events)
        self.assertEqual(categorized_events["Warning"], [event_mock])
        self.assertIn("Warning", categorized_recipients)
        self.assertIn(self.user_mock, categorized_recipients["Warning"]["email"])
        self.assertEqual(len(categorized_recipients["Warning"]["sms"]), 0)


@tag('integration')
class UtilsIntegrationTests(TestCase):
    """Integration tests for utility functions with real database objects"""

    def setUp(self):
        # Create real User
        self.user = User.objects.create_user(
            username="testuser",
            password="testpassword"
        )

        # Create real Field object with proper polygon coordinates
        self.field = Field.objects.create(
            name="Test Field",
            cord=[
                {"lat": 23.5, "lng": 55.5},
                {"lat": 23.6, "lng": 55.5},
                {"lat": 23.6, "lng": 55.6},
                {"lat": 23.5, "lng": 55.6}
            ],
            colr="#FF0000",
            covr=10.0,
            loca="Test Location"
        )

        # Create real Device object
        self.device = Device.objects.create(
            name="Test Device",
            desc="Test device description",
            euid="ABCDEF0123456789",
            type="Whiskers Node V1",
            fild=self.field
        )

        # Create real NotificationSettings object
        self.notification_settings = NotificationSettings.objects.create(
            user=self.user,
            rxif=True,
            rxup=True,
            rxwr=True,
            rxdg=True,
            mthd="Email"
        )
        self.notification_settings.devs.add(self.device)

        # Create real Event object
        self.event = Event.objects.create(
            devi=self.device,
            type="Warning",
            desc="Test warning event"
        )

        # Create real Notification object
        self.notification = Notification.objects.create(
            user=self.user,
            evnt=self.event,
            read=False,
            sent=False
        )

    def test_should_send_notifications(self):
        """Test should_send_notifications function with real database objects"""
        # Create events
        event1 = Event.objects.create(
            devi=self.device,
            type="Warning",
            desc="Test warning event"
        )

        event2 = Event.objects.create(
            devi=self.device,
            type="Danger",
            desc="Test danger event"
        )

        # Create notification for event1 and mark as sent
        notification1 = Notification.objects.create(
            user=self.user,
            evnt=event1,
            sent=True
        )

        # Test with events that should be sent
        events_to_send = should_send_notifications(self.device, "Danger", [event2])
        self.assertEqual(len(events_to_send), 1)
        self.assertEqual(events_to_send[0], event2)

        # Test with duplicate event that was recently sent
        events_to_send = should_send_notifications(self.device, "Warning", [event1])
        self.assertEqual(len(events_to_send), 0)

    @patch('notification_center.utils.Event.save')
    def test_process_events(self, mock_save):
        """Test process_events function with real database objects"""
        # Create event list
        event1 = Event(
            devi=self.device,
            type="Warning",
            desc="Test warning event"
        )

        event2 = Event(
            devi=self.device,
            type="Danger",
            desc="Test danger event"
        )

        # Call function
        process_events([event1, event2])

        # Check that save was called for each event
        self.assertEqual(mock_save.call_count, 2)

    def test_handle_duplicate_events(self):
        """Test handle_duplicate_events function with real database objects"""
        # Create events
        event1 = Event.objects.create(
            devi=self.device,
            type="Warning",
            desc="Test warning event"
        )

        event2 = Event.objects.create(
            devi=self.device,
            type="Warning",
            desc="Test warning event"
        )

        # Create notifications for the events
        notification1 = Notification.objects.create(
            user=self.user,
            evnt=event1,
            sent=False
        )

        notification2 = Notification.objects.create(
            user=self.user,
            evnt=event2,
            sent=False
        )

        # Call handle_duplicate_events
        from notification_center.utils import handle_duplicate_events
        handle_duplicate_events([event2])

        # Check that the notification for the duplicate event was marked as sent
        notification2.refresh_from_db()
        self.assertTrue(notification2.sent)

        # Check that the original notification was not affected
        notification1.refresh_from_db()
        self.assertFalse(notification1.sent)

    def test_categorize_events_with_real_objects(self):
        """Test categorize_events function with real database objects"""
        # Create events of different types
        event1 = Event.objects.create(
            devi=self.device,
            type="Warning",
            desc="Test warning event"
        )

        event2 = Event.objects.create(
            devi=self.device,
            type="Danger",
            desc="Test danger event"
        )

        event3 = Event.objects.create(
            devi=self.device,
            type="Info",
            desc="Test info event"
        )

        # Call categorize_events
        from notification_center.utils import categorize_events
        categorized_events, categorized_recipients = categorize_events([event1, event2, event3])

        # Check that events are categorized correctly
        self.assertIn("Warning", categorized_events)
        self.assertIn("Danger", categorized_events)
        self.assertIn("Info", categorized_events)
        self.assertEqual(len(categorized_events["Warning"]), 1)
        self.assertEqual(len(categorized_events["Danger"]), 1)
        self.assertEqual(len(categorized_events["Info"]), 1)

        # Check that recipients are categorized correctly
        self.assertIn("Warning", categorized_recipients)
        self.assertIn("Danger", categorized_recipients)
        self.assertIn("Info", categorized_recipients)

    def test_deduplicate_events_with_real_objects(self):
        """Test deduplicate_events function with real database objects"""
        # Create duplicate events
        event1 = Event.objects.create(
            devi=self.device,
            type="Warning",
            desc="Test warning event"
        )

        event2 = Event.objects.create(
            devi=self.device,
            type="Warning",
            desc="Test warning event"  # Same description
        )

        event3 = Event.objects.create(
            devi=self.device,
            type="Danger",
            desc="Test danger event"  # Different description
        )

        # Call deduplicate_events
        from notification_center.utils import deduplicate_events
        unique_events, duplicate_events = deduplicate_events([event1, event2, event3])

        # Check that duplicates are identified correctly
        self.assertEqual(len(unique_events), 2)  # event1 and event3
        self.assertEqual(len(duplicate_events), 1)  # event2
        self.assertIn(event1, unique_events)
        self.assertIn(event3, unique_events)
        self.assertIn(event2, duplicate_events)

    def test_send_notifications_with_real_objects(self):
        """Test send_notifications function with real database objects"""
        # Create events
        event1 = Event.objects.create(
            devi=self.device,
            type="Warning",
            desc="Test warning event"
        )

        # Get or create notification settings
        notification_settings, created = NotificationSettings.objects.get_or_create(
            user=self.user,
            defaults={
                'mthd': 'Email',
                'rxwr': True
            }
        )
        if not created:
            notification_settings.mthd = 'Email'
            notification_settings.rxwr = True
            notification_settings.save()
        notification_settings.devs.add(self.device)

        # Create categorized events and recipients
        categorized_events = {"Warning": [event1]}
        categorized_recipients = {
            "Warning": {
                "email": {self.user.email},
                "sms": set()
            }
        }

        # Mock the communication functions
        with patch('scripts.communication.send_email_notification') as mock_email, \
             patch('scripts.communication.send_sms_notification') as mock_sms:

            mock_email.return_value = True
            mock_sms.return_value = True

            # Call send_notifications
            from notification_center.utils import send_notifications
            send_notifications(categorized_events, categorized_recipients, self.device)

            # Check that email notification was attempted
            mock_email.assert_called_once()

    def test_generate_global_events_low_battery(self):
        """Test generate_global_events function with low battery"""
        # Set device battery to low
        self.device.batt = 3
        self.device.save()

        # Call generate_global_events
        from notification_center.utils import generate_global_events
        generate_global_events(self.device)

        # Check that a warning event was created
        events = Event.objects.filter(devi=self.device, type="Warning", desc="has low battery.")
        self.assertTrue(events.exists())

    def test_generate_global_events_high_temperature(self):
        """Test generate_global_events function with high temperature"""
        # Set device temperature to high
        self.device.temp = 75
        self.device.save()

        # Call generate_global_events
        from notification_center.utils import generate_global_events
        generate_global_events(self.device)

        # Check that a warning event was created
        events = Event.objects.filter(devi=self.device, type="Warning", desc="has high temperature.")
        self.assertTrue(events.exists())

    def test_check_back_online(self):
        """Test check_back_online function"""
        # Set device status to offline
        self.device.stat = "Offline"
        self.device.save()

        # Call check_back_online
        from notification_center.utils import check_back_online
        check_back_online(self.device)

        # Check that an update event was created
        events = Event.objects.filter(devi=self.device, type="Update", desc="is back online.")
        self.assertTrue(events.exists())

    def test_process_events(self):
        """Test process_events function"""
        # Create a list of events
        event_list = [
            Event(devi=self.device, type="Warning", desc="Test event 1"),
            Event(devi=self.device, type="Info", desc="Test event 2")
        ]

        # Call process_events
        from notification_center.utils import process_events
        process_events(event_list)

        # Check that events were saved
        saved_events = Event.objects.filter(devi=self.device, desc__in=["Test event 1", "Test event 2"])
        self.assertEqual(saved_events.count(), 2)

    def test_check_triggers_motion_detection(self):
        """Test check_triggers function with motion detection"""
        # Set device attributes for motion detection
        self.device.actv = False
        self.device.attr = {
            "client": {
                "Motion event.": True,
                "Motionless event.": False,
                "Shock event.": False
            }
        }
        self.device.save()

        # Call check_triggers with outside work hours
        from notification_center.utils import check_triggers
        check_triggers(self.device, outside_work_hours=True)

        # Check that a danger event was created
        events = Event.objects.filter(devi=self.device, type="Danger", desc="has been moved.")
        self.assertTrue(events.exists())

        # The device status should be updated by the function, but we need to save it
        # Let's check if the event was created instead
        self.assertTrue(events.exists())

    def test_check_triggers_shock_detection(self):
        """Test check_triggers function with shock detection"""
        # Set device attributes for shock detection
        self.device.attr = {
            "client": {
                "Motion event.": False,
                "Motionless event.": False,
                "Shock event.": True
            }
        }
        self.device.save()

        # Call check_triggers with outside work hours
        from notification_center.utils import check_triggers
        check_triggers(self.device, outside_work_hours=True)

        # Check that a danger event was created
        events = Event.objects.filter(devi=self.device, type="Danger", desc="detected a shock.")
        self.assertTrue(events.exists())

        # The device status should be updated by the function, but we need to save it
        # Let's check if the event was created instead
        self.assertTrue(events.exists())

    def test_check_out_of_field(self):
        """Test check_out_of_field function"""
        # Set device location outside the field
        self.device.loca = {
            "lati": 25.0,  # Outside the field coordinates
            "long": 56.0,
            "oofi": False
        }
        check_back_online(self.device)
        
        # Call check_out_of_field
        from notification_center.utils import check_out_of_field
        check_out_of_field(self.device)

        self.device.save()

        # Check that a warning event was created
        events = Event.objects.filter(devi=self.device, type="Warning", desc="is out of assigned field.")
        self.assertTrue(events.exists())

        # Check that device status was updated
        self.device.refresh_from_db()
        self.assertEqual(self.device.stat, "Warning")
        self.assertTrue(self.device.loca["oofi"])

    def test_check_gateway_node_power_disconnect(self):
        """Test check_gateway_node function with power disconnect"""
        # Create a gateway device
        gateway = Device.objects.create(
            name="GWN Test Gateway",
            desc="Test gateway device",
            euid="GATEWAY123456789",
            type="Gateway",
            fild=self.field,
            chrg=True,
            stat='Offline',
            attr={"client": {"temp": 25}}
        )
        
        # Set device attributes for power disconnect
        self.device.name = "GWN Test Device"
        self.device.attr = {
            "client": {
                "Light": 30,  # Low light indicating power disconnect
                "Motion event.": False,
                "Motionless event.": False,
                "Shock event.": False,
            }
        }
        check_back_online(self.device)
        self.device.save()

        # Call check_gateway_node
        from notification_center.utils import check_gateway_node
        check_gateway_node(self.device, gateway, outside_work_hours=False)
        gateway.save()

        # Check that a warning event was created
        events = Event.objects.filter(devi=gateway, type="Warning", desc="disconnected from main power.")
        self.assertTrue(events.exists())

        # Check that gateway charging status was updated
        gateway.refresh_from_db()
        self.assertFalse(gateway.chrg)
        self.assertEqual(gateway.stat, "Warning")
