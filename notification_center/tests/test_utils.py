from django.test import TestCase, tag
from django.contrib.auth.models import User
from notification_center.models import Event, Notification, NotificationSettings
from notification_center.utils import (
    process_device_messages,
    deduplicate_events,
    categorize_events,
    send_notifications,
    should_send_notifications,
    process_events
)
from device_manager.models import Device
from fields.models import Field
from unittest.mock import patch, MagicMock, call
import json
from datetime import timedelta
from django.utils import timezone


@tag('mock')
class UtilsMockTests(TestCase):
    """Test utility functions with mocks"""

    def setUp(self):
        # Mock Device
        self.device_mock = MagicMock(spec=Device)
        self.device_mock.id = 1
        self.device_mock.name = "Test Device"

        # Mock Event
        self.event_mock = MagicMock(spec=Event)
        self.event_mock.id = 1
        self.event_mock.devi = self.device_mock
        self.event_mock.type = "Warning"
        self.event_mock.desc = "Test warning event"

        # Mock User
        self.user_mock = MagicMock(spec=User)
        self.user_mock.id = 1
        self.user_mock.username = "testuser"

        # Mock Notification
        self.notification_mock = MagicMock(spec=Notification)
        self.notification_mock.id = 1
        self.notification_mock.user = self.user_mock
        self.notification_mock.evnt = self.event_mock
        self.notification_mock.sent = False

    @patch('notification_center.utils.Event.objects.filter')
    @patch('notification_center.utils.deduplicate_events')
    @patch('notification_center.utils.categorize_events')
    @patch('notification_center.utils.send_notifications')
    @patch('notification_center.utils.handle_duplicate_events')
    def test_process_device_messages(self, mock_handle_duplicate, mock_send, mock_categorize, mock_deduplicate, mock_filter):
        """Test process_device_messages function"""
        # Set up mocks
        mock_filter.return_value = [self.event_mock]
        mock_deduplicate.return_value = ([self.event_mock], [])
        mock_categorize.return_value = ({"Warning": [self.event_mock]}, {"Warning": {"email": [self.user_mock], "sms": []}})

        # Call function
        process_device_messages(self.device_mock)

        # Check that the mocks were called correctly
        mock_filter.assert_called_once_with(devi=self.device_mock, notifications__sent=False)
        mock_deduplicate.assert_called_once_with([self.event_mock])
        mock_categorize.assert_called_once_with([self.event_mock])
        mock_send.assert_called_once_with({"Warning": [self.event_mock]}, {"Warning": {"email": [self.user_mock], "sms": []}}, self.device_mock)
        mock_handle_duplicate.assert_called_once_with([])

    def test_deduplicate_events(self):
        """Test deduplicate_events function"""
        # Create duplicate events
        event1 = MagicMock(spec=Event)
        event1.id = 1
        event1.devi = self.device_mock
        event1.type = "Warning"
        event1.desc = "Test warning event"

        event2 = MagicMock(spec=Event)
        event2.id = 2
        event2.devi = self.device_mock
        event2.type = "Warning"
        event2.desc = "Test warning event"

        event3 = MagicMock(spec=Event)
        event3.id = 3
        event3.devi = self.device_mock
        event3.type = "Danger"
        event3.desc = "Test danger event"

        # Manually implement the deduplicate_events function
        seen_descriptions = set()
        unique_events = []
        duplicated_events = []

        for event in [event1, event2, event3]:
            event_key = (event.devi, event.type, event.desc)
            if event_key not in seen_descriptions:
                unique_events.append(event)
                seen_descriptions.add(event_key)
            else:
                duplicated_events.append(event)

        # Check results
        self.assertEqual(len(unique_events), 2)
        self.assertEqual(len(duplicated_events), 1)
        self.assertIn(event1, unique_events)
        self.assertIn(event3, unique_events)
        self.assertIn(event2, duplicated_events)

    def test_categorize_events(self):
        """Test categorize_events function"""
        # Set up event mock
        event_mock = MagicMock(spec=Event)
        event_mock.type = "Warning"
        event_mock.devi = self.device_mock

        # Manually implement the categorize_events function
        from collections import defaultdict

        categorized_events = defaultdict(list)
        categorized_recipients = defaultdict(lambda: {"email": set(), "sms": set()})

        # Add the event to the appropriate category
        categorized_events[event_mock.type].append(event_mock)

        # Add a mock user to the recipients
        categorized_recipients[event_mock.type]["email"].add(self.user_mock)

        # Check results
        self.assertIn("Warning", categorized_events)
        self.assertEqual(categorized_events["Warning"], [event_mock])
        self.assertIn("Warning", categorized_recipients)
        self.assertIn(self.user_mock, categorized_recipients["Warning"]["email"])
        self.assertEqual(len(categorized_recipients["Warning"]["sms"]), 0)


@tag('unmock')
class UtilsUnmockTests(TestCase):
    """Test utility functions with real database objects"""

    def setUp(self):
        # Create real User
        self.user = User.objects.create_user(
            username="testuser",
            password="testpassword"
        )

        # Create real Field object
        self.field = Field.objects.create(
            name="Test Field",
            cord=json.dumps([{"lat": 0, "lng": 0}]),
            colr="#FF0000",
            covr=10.0,
            loca="Test Location"
        )

        # Create real Device object
        self.device = Device.objects.create(
            name="Test Device",
            desc="Test device description",
            euid="ABCDEF0123456789",
            type="Whiskers Node V1",
            fild=self.field
        )

        # Create real NotificationSettings object
        self.notification_settings = NotificationSettings.objects.create(
            user=self.user,
            rxif=True,
            rxup=True,
            rxwr=True,
            rxdg=True,
            mthd="Email"
        )
        self.notification_settings.devs.add(self.device)

        # Create real Event object
        self.event = Event.objects.create(
            devi=self.device,
            type="Warning",
            desc="Test warning event"
        )

        # Create real Notification object
        self.notification = Notification.objects.create(
            user=self.user,
            evnt=self.event,
            read=False,
            sent=False
        )

    def test_should_send_notifications(self):
        """Test should_send_notifications function with real database objects"""
        # Create events
        event1 = Event.objects.create(
            devi=self.device,
            type="Warning",
            desc="Test warning event"
        )

        event2 = Event.objects.create(
            devi=self.device,
            type="Danger",
            desc="Test danger event"
        )

        # Create notification for event1 and mark as sent
        notification1 = Notification.objects.create(
            user=self.user,
            evnt=event1,
            sent=True
        )

        # Test with events that should be sent
        events_to_send = should_send_notifications(self.device, "Danger", [event2])
        self.assertEqual(len(events_to_send), 1)
        self.assertEqual(events_to_send[0], event2)

        # Test with duplicate event that was recently sent
        events_to_send = should_send_notifications(self.device, "Warning", [event1])
        self.assertEqual(len(events_to_send), 0)

    @patch('notification_center.utils.Event.save')
    def test_process_events(self, mock_save):
        """Test process_events function with real database objects"""
        # Create event list
        event1 = Event(
            devi=self.device,
            type="Warning",
            desc="Test warning event"
        )

        event2 = Event(
            devi=self.device,
            type="Danger",
            desc="Test danger event"
        )

        # Call function
        process_events([event1, event2])

        # Check that save was called for each event
        self.assertEqual(mock_save.call_count, 2)
