from django.test import TestCase, tag
from django.contrib.auth.models import User
from notification_center.models import Event, Notification, NotificationSettings
from device_manager.models import Device
from fields.models import Field
from unittest.mock import patch, MagicMock
import json


@tag('mock')
class EventModelMockTests(TestCase):
    """Test Event model with mocks"""

    def test_event_str(self):
        """Test the string representation of an Event"""
        # Set up mock device
        device = MagicMock()
        device.name = "Test Device"

        # Set up mock event
        event = MagicMock()
        event.devi = device
        event.type = "Warning"
        event.desc = "Test warning event"

        # Manually implement the __str__ method
        result = f"{event.type}: {event.devi.name} {event.desc}"

        # Verify the result
        self.assertEqual(result, "Warning: Test Device Test warning event")

    def test_event_to_dict(self):
        """Test the to_dict method of Event"""
        # Create a mock event
        event = MagicMock()
        event.id = 1
        event.type = "Warning"
        event.desc = "Test warning event"

        # Mock the formatted_crat
        event.crat.strftime.return_value = '01 January 2024 — 12:00:00 PM'

        # Mock the device
        device = MagicMock()
        device.id = 1
        device.name = "Test Device"
        event.devi = device

        # Manually implement the to_dict method
        result = {
            'id': event.id,
            'type': event.type,
            'desc': event.desc,
            'devi': {
                'id': event.devi.id,
                'name': event.devi.name,
            },
            'crat': event.crat.strftime.return_value,
        }

        # Verify the result
        self.assertEqual(result['id'], 1)
        self.assertEqual(result['type'], "Warning")
        self.assertEqual(result['desc'], "Test warning event")
        self.assertEqual(result['devi']['id'], 1)
        self.assertEqual(result['devi']['name'], "Test Device")


@tag('mock')
class NotificationModelMockTests(TestCase):
    """Test Notification model with mocks"""

    def test_notification_to_dict(self):
        """Test the to_dict method of Notification"""
        # Create a mock notification
        notification = MagicMock()
        notification.id = 1

        # Mock user
        user = MagicMock()
        user.username = "testuser"
        notification.user = user

        # Mock event
        event = MagicMock()
        event.to_dict.return_value = {
            'id': 1,
            'type': 'Warning',
            'desc': 'Test warning event',
            'devi': {'id': 1, 'name': 'Test Device'},
            'crat': '01 January 2024 — 12:00:00 PM'
        }
        notification.evnt = event

        # Manually implement the to_dict method
        result = {
            'id': notification.id,
            'user': notification.user.username,
            'evnt': notification.evnt.to_dict(),
        }

        # Verify the result
        self.assertEqual(result['id'], 1)
        self.assertEqual(result['user'], "testuser")
        self.assertEqual(result['evnt']['id'], 1)
        self.assertEqual(result['evnt']['type'], "Warning")


@tag('mock')
class NotificationSettingsModelMockTests(TestCase):
    """Test NotificationSettings model with mocks"""

    def test_notification_settings_str(self):
        """Test the string representation of NotificationSettings"""
        # Set up mock user
        user = MagicMock()
        user.username = "testuser"

        # Set up mock notification settings
        notification_settings = MagicMock()
        notification_settings.user = user

        # Manually implement the __str__ method
        result = notification_settings.user.username

        # Verify the result
        self.assertEqual(result, "testuser")


@tag('unmock')
class EventModelUnmockTests(TestCase):
    """Test Event model with real database objects"""

    def setUp(self):
        # Create real Field object
        self.field = Field.objects.create(
            name="Test Field",
            cord=json.dumps([{"lat": 0, "lng": 0}]),
            colr="#FF0000",
            covr=10.0,
            loca="Test Location"
        )

        # Create real Device object
        self.device = Device.objects.create(
            name="Test Device",
            desc="Test device description",
            euid="ABCDEF0123456789",
            type="Whiskers Node V1",
            fild=self.field
        )

        # Create real Event object
        self.event = Event.objects.create(
            devi=self.device,
            type="Warning",
            desc="Test warning event"
        )

    def test_event_creation(self):
        """Test creating an Event with real database objects"""
        self.assertEqual(self.event.devi, self.device)
        self.assertEqual(self.event.type, "Warning")
        self.assertEqual(self.event.desc, "Test warning event")

    def test_event_str(self):
        """Test the string representation of an Event"""
        self.assertEqual(str(self.event), f"Warning: {self.device.name} Test warning event")

    def test_event_to_dict(self):
        """Test the to_dict method of Event"""
        event_dict = self.event.to_dict()
        self.assertEqual(event_dict['id'], self.event.id)
        self.assertEqual(event_dict['type'], "Warning")
        self.assertEqual(event_dict['desc'], "Test warning event")
        self.assertEqual(event_dict['devi']['id'], self.device.id)
        self.assertEqual(event_dict['devi']['name'], self.device.name)

    def test_event_with_long_description(self):
        """Test Event with a very long description"""
        # The desc field has a max length of 255 characters
        long_desc = "This is a long description " * 8  # ~200 characters
        event = Event.objects.create(
            devi=self.device,
            type="Warning",
            desc=long_desc
        )
        self.assertEqual(event.desc, long_desc)
        event_dict = event.to_dict()
        self.assertEqual(event_dict['desc'], long_desc)

    def test_event_with_special_characters(self):
        """Test Event with special characters in description"""
        special_desc = "Test with special characters: !@#$%^&*()_+{}[]|\\:;\"'<>,.?/"
        event = Event.objects.create(
            devi=self.device,
            type="Warning",
            desc=special_desc
        )
        self.assertEqual(event.desc, special_desc)
        event_dict = event.to_dict()
        self.assertEqual(event_dict['desc'], special_desc)

    def test_event_with_different_types(self):
        """Test Event with different types"""
        types = ["Info", "Warning", "Danger", "Critical"]
        for event_type in types:
            event = Event.objects.create(
                devi=self.device,
                type=event_type,
                desc=f"Test {event_type.lower()} event"
            )
            self.assertEqual(event.type, event_type)
            event_dict = event.to_dict()
            self.assertEqual(event_dict['type'], event_type)


@tag('unmock')
class NotificationModelUnmockTests(TestCase):
    """Test Notification model with real database objects"""

    def setUp(self):
        # Create real User
        self.user = User.objects.create_user(
            username="testuser",
            password="testpassword"
        )

        # Create real Field object
        self.field = Field.objects.create(
            name="Test Field",
            cord=json.dumps([{"lat": 0, "lng": 0}]),
            colr="#FF0000",
            covr=10.0,
            loca="Test Location"
        )

        # Create real Device object
        self.device = Device.objects.create(
            name="Test Device",
            desc="Test device description",
            euid="ABCDEF0123456789",
            type="Whiskers Node V1",
            fild=self.field
        )

        # Create real Event object
        self.event = Event.objects.create(
            devi=self.device,
            type="Warning",
            desc="Test warning event"
        )

        # Create real Notification object
        self.notification = Notification.objects.create(
            user=self.user,
            evnt=self.event,
            read=False,
            sent=False,
            fail="No Error."
        )

    def test_notification_creation(self):
        """Test creating a Notification with real database objects"""
        self.assertEqual(self.notification.user, self.user)
        self.assertEqual(self.notification.evnt, self.event)
        self.assertEqual(self.notification.read, False)
        self.assertEqual(self.notification.sent, False)
        self.assertEqual(self.notification.fail, "No Error.")

    def test_notification_to_dict(self):
        """Test the to_dict method of Notification"""
        notification_dict = self.notification.to_dict()
        self.assertEqual(notification_dict['id'], self.notification.id)
        self.assertEqual(notification_dict['user'], self.user.username)
        self.assertEqual(notification_dict['evnt']['id'], self.event.id)

    def test_notification_mark_as_read(self):
        """Test marking a notification as read"""
        self.notification.read = True
        self.notification.save()

        # Fetch the notification from the database again
        updated_notification = Notification.objects.get(id=self.notification.id)
        self.assertTrue(updated_notification.read)

    def test_notification_mark_as_sent(self):
        """Test marking a notification as sent"""
        self.notification.sent = True
        self.notification.save()

        # Fetch the notification from the database again
        updated_notification = Notification.objects.get(id=self.notification.id)
        self.assertTrue(updated_notification.sent)

    def test_notification_with_error(self):
        """Test notification with an error message"""
        error_message = "Failed to send notification: Connection error"
        self.notification.fail = error_message
        self.notification.save()

        # Fetch the notification from the database again
        updated_notification = Notification.objects.get(id=self.notification.id)
        self.assertEqual(updated_notification.fail, error_message)

    def test_notification_delete(self):
        """Test deleting a notification"""
        notification_id = self.notification.id
        self.notification.delete()

        # Check that the notification no longer exists
        with self.assertRaises(Notification.DoesNotExist):
            Notification.objects.get(id=notification_id)


@tag('unmock')
class NotificationSettingsModelUnmockTests(TestCase):
    """Test NotificationSettings model with real database objects"""

    def setUp(self):
        # Create real User
        self.user = User.objects.create_user(
            username="testuser",
            password="testpassword"
        )

        # Create real NotificationSettings object
        self.notification_settings = NotificationSettings.objects.create(
            user=self.user,
            rxif=True,
            rxup=True,
            rxwr=True,
            rxdg=True,
            mthd="Email"
        )

    def test_notification_settings_creation(self):
        """Test creating NotificationSettings with real database objects"""
        self.assertEqual(self.notification_settings.user, self.user)
        self.assertEqual(self.notification_settings.rxif, True)
        self.assertEqual(self.notification_settings.rxup, True)
        self.assertEqual(self.notification_settings.rxwr, True)
        self.assertEqual(self.notification_settings.rxdg, True)
        self.assertEqual(self.notification_settings.mthd, "Email")

    def test_notification_settings_str(self):
        """Test the string representation of NotificationSettings"""
        self.assertEqual(str(self.notification_settings), self.user.username)
