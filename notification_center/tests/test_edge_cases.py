from django.test import TestCase, tag
from django.urls import reverse
from django.contrib.auth.models import User
from notification_center.models import Event, Notification, NotificationSettings
from notification_center.utils import process_device_messages, send_notifications
from device_manager.models import Device
from fields.models import Field
from accounts.models import UserProfile
from unittest.mock import patch, MagicMock
import json


@tag('edge_cases')
class NetworkFailureTests(TestCase):
    """Test edge cases related to network failures"""

    def setUp(self):
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            password='testpassword',
            email='<EMAIL>'
        )

        # Create test profile
        self.profile = UserProfile.objects.create(
            user=self.user,
            role='User',
            phon='********',
            titl='Test Title',
            orgn='Test Organization'
        )

        # Create real Field object
        self.field = Field.objects.create(
            name="Test Field",
            cord=json.dumps([{"lat": 0, "lng": 0}]),
            colr="#FF0000",
            covr=10.0,
            loca="Test Location"
        )

        # Create real Device object
        self.device = Device.objects.create(
            name="Test Device",
            desc="Test device description",
            euid="ABCDEF0********9",
            type="Whiskers Node V1",
            fild=self.field
        )

        # Associate device with user profile
        self.profile.devs.add(self.device)

        # Create notification settings
        self.notification_settings = NotificationSettings.objects.create(
            user=self.user,
            rxif=True,
            rxup=True,
            rxwr=True,
            rxdg=True,
            mthd="Email"
        )
        self.notification_settings.devs.add(self.device)

    def test_notification_with_error_message(self):
        """Test notification with error message"""
        # Create an event
        event = Event.objects.create(
            devi=self.device,
            type="Warning",
            desc="Test warning event"
        )

        # Create a notification with an error message
        error_message = "Failed to send: Network timeout"
        notification = Notification.objects.create(
            user=self.user,
            evnt=event,
            read=False,
            sent=False,
            fail=error_message
        )

        # Check that the error message was saved correctly
        self.assertEqual(notification.fail, error_message)

        # Check that the notification appears in the list view
        self.client.login(username='testuser', password='testpassword')
        response = self.client.get(reverse('notification_center:list'))
        self.assertEqual(response.status_code, 200)
        self.assertIn(notification, response.context['list'])

    def test_notification_status_update(self):
        """Test updating notification status"""
        # Create an event
        event = Event.objects.create(
            devi=self.device,
            type="Warning",
            desc="Test warning event"
        )

        # Create a notification
        notification = Notification.objects.create(
            user=self.user,
            evnt=event,
            read=False,
            sent=False
        )

        # Update notification status
        notification.read = True
        notification.sent = True
        notification.save()

        # Refresh the notification from the database
        notification.refresh_from_db()

        # Check that the status was updated correctly
        self.assertTrue(notification.read)
        self.assertTrue(notification.sent)



    def test_multiple_notifications_for_same_event(self):
        """Test creating multiple notifications for the same event for different users"""
        # Create another user
        user2 = User.objects.create_user(
            username='testuser2',
            password='testpassword',
            email='<EMAIL>'
        )

        # Create an event
        event = Event.objects.create(
            devi=self.device,
            type="Warning",
            desc="Test warning event"
        )

        # Create notifications for both users
        notification1 = Notification.objects.create(
            user=self.user,
            evnt=event,
            read=False,
            sent=False
        )

        notification2 = Notification.objects.create(
            user=user2,
            evnt=event,
            read=False,
            sent=False
        )

        # Check that both notifications were created correctly
        self.assertEqual(notification1.evnt, event)
        self.assertEqual(notification2.evnt, event)
        self.assertEqual(notification1.user, self.user)
        self.assertEqual(notification2.user, user2)

    def test_notification_deletion(self):
        """Test deleting a notification"""
        # Create an event
        event = Event.objects.create(
            devi=self.device,
            type="Warning",
            desc="Test warning event"
        )

        # Create a notification
        notification = Notification.objects.create(
            user=self.user,
            evnt=event,
            read=False,
            sent=False
        )

        # Get the notification ID
        notification_id = notification.id

        # Delete the notification
        notification.delete()

        # Check that the notification no longer exists
        with self.assertRaises(Notification.DoesNotExist):
            Notification.objects.get(id=notification_id)
